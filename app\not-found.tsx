import { generateMetadata as generateSEOMetadata } from "@/lib/seo";
import { Metadata } from "next";
import Link from "next/link";

// SEO metadata for 404 page
export const metadata: Metadata = generateSEOMetadata({
   title: "Page Not Found - Beckizedek",
   description:
      "The page you're looking for doesn't exist. Explore Beckizedek's creative services, brand storytelling, and marketing solutions.",
   url: "https://www.beckizedek.com/404",
});

export default function NotFound() {
   return (
      <div className="bg-background flex min-h-screen items-center justify-center px-5">
         <div className="mx-auto max-w-2xl text-center">
            <h1 className="font-anton text-primary mb-8 text-[clamp(4rem,15vw,12rem)] leading-none font-bold tracking-wider">
               404
            </h1>

            <h2 className="font-editorial-old text-primary mb-6 text-3xl font-thin md:text-4xl lg:text-5xl">
               Page Not Found
            </h2>

            <p className="font-gilroy text-primary/80 mb-12 text-lg leading-relaxed md:text-xl">
               The page you&apos;re looking for doesn&apos;t exist. But
               don&apos;t worry – there&apos;s plenty of creative content and
               brand storytelling insights waiting for you on the homepage.
            </p>

            <div className="space-y-4 sm:flex sm:justify-center sm:space-y-0 sm:space-x-6">
               <Link
                  href="/"
                  className="font-gilroy bg-primary hover:bg-primary/90 inline-block rounded-full px-8 py-3 font-medium tracking-wide text-white transition-all duration-300 hover:scale-105"
               >
                  Go Home
               </Link>

               <Link
                  href="/#contact"
                  className="font-gilroy border-primary text-primary hover:bg-primary inline-block rounded-full border-2 px-8 py-3 font-medium tracking-wide transition-all duration-300 hover:text-white"
               >
                  Contact Me
               </Link>
            </div>

            {/* Helpful links */}
            <div className="border-primary/20 mt-16 border-t pt-8">
               <p className="font-gilroy text-primary/60 mb-4 text-sm">
                  Looking for something specific?
               </p>
               <nav className="flex flex-wrap justify-center gap-6 text-sm">
                  <Link
                     href="/#about"
                     className="text-primary/80 hover:text-primary transition-colors"
                  >
                     About
                  </Link>
                  <Link
                     href="/#services"
                     className="text-primary/80 hover:text-primary transition-colors"
                  >
                     Services
                  </Link>
                  <Link
                     href="/#brands"
                     className="text-primary/80 hover:text-primary transition-colors"
                  >
                     Trusted Brands
                  </Link>
                  <Link
                     href="/#what-i-do"
                     className="text-primary/80 hover:text-primary transition-colors"
                  >
                     What I Do
                  </Link>
               </nav>
            </div>
         </div>
      </div>
   );
}
