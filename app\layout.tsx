import ClientLayout from "@/components/client-layout";
import { LoadingProvider } from "@/components/providers/loading-provider";
import { editorialOld, gilroy } from "@/lib/fonts";
import {
   generateMetadata as generateSEOMetadata,
   generateStructuredData,
} from "@/lib/seo";
import { GoogleAnalytics } from "@next/third-parties/google";
import type { Metadata } from "next";
import { <PERSON>, Geist_Mono, Inter, Manrope } from "next/font/google";
import Script from "next/script";
import "./globals.css";

const inter = Inter({
   variable: "--font-inter",
   subsets: ["latin"],
});

const manrope = Manrope({
   variable: "--font-manrope",
   subsets: ["latin"],
   weight: ["200", "300", "400", "500", "600", "700", "800"],
});

const geistMono = Geist_Mono({
   variable: "--font-geist-mono",
   subsets: ["latin"],
});

const anton = <PERSON>({
   variable: "--font-anton",
   subsets: ["latin"],
   weight: "400",
});

// Generate comprehensive SEO metadata
export const metadata: Metadata = generateSEOMetadata();

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   const structuredData = generateStructuredData();

   return (
      <html lang="en">
         <head>
            {/* Structured Data - Organization/Person Schema */}
            <Script
               id="organization-schema"
               type="application/ld+json"
               dangerouslySetInnerHTML={{
                  __html: JSON.stringify(structuredData.organization),
               }}
            />
            {/* Structured Data - Website Schema */}
            <Script
               id="website-schema"
               type="application/ld+json"
               dangerouslySetInnerHTML={{
                  __html: JSON.stringify(structuredData.website),
               }}
            />
            {/* Structured Data - Services Schema */}
            <Script
               id="services-schema"
               type="application/ld+json"
               dangerouslySetInnerHTML={{
                  __html: JSON.stringify(structuredData.services),
               }}
            />
         </head>
         <GoogleAnalytics gaId="G-GB5Z446KDG" />
         <body
            className={`${geistMono.variable} ${anton.variable} ${inter.variable} ${editorialOld.variable} ${gilroy.variable} ${manrope.variable} antialiased`}
         >
            <LoadingProvider>
               <ClientLayout>{children}</ClientLayout>
            </LoadingProvider>
         </body>
      </html>
   );
}
