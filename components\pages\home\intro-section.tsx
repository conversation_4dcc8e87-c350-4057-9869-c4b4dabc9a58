"use client";

import InfiniteScroll from "@/components/animations/infinite-scroll/infinite-scroll";
import { useScroll } from "motion/react";
import { useRef } from "react";
import Scene from "./3d-mesh/scene";

const roles = [
   "Creative Director",
   "Content Strategist",
   "Brand Storyteller",
   "Marketing Campaign Lead",
   "Campaign Video Director",
   "Event Storytelling Specialist",
];

export default function IntroSection() {
   const container = useRef<HTMLDivElement | null>(null);

   const { scrollYProgress } = useScroll({
      target: container,
      offset: ["start start", "end end"],
   });

   return (
      <section
         ref={container}
         className="bg-primary relative h-[calc(200vh)] pt-10 text-[#bedbff]"
      >
         <div className="font-anton sticky top-6 h-screen overflow-hidden text-[16vh] leading-none font-thin tracking-wider uppercase">
            {roles.map((role, index) => (
               <InfiniteScroll
                  key={role}
                  className="h-fit"
                  innerClassName="!gap-30 !p-0"
                  speed="slow"
                  direction={`${index % 2 === 0 ? "left" : "right"}`}
                  showMask={false}
               >
                  <span>{role}</span>
                  <span>{roles[index]}</span>
               </InfiniteScroll>
            ))}
            <div className="absolute inset-0 z-100 flex items-center justify-center">
               <Scene scrollProgress={scrollYProgress} />
            </div>
         </div>
      </section>
   );
}
