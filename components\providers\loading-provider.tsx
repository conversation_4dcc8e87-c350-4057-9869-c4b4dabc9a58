"use client";

import { createContext, ReactNode, useContext, useState } from "react";

interface LoadingContextType {
   isLoading: boolean;
   setIsLoading: (loading: boolean) => void;
   isLoadingComplete: boolean;
   setIsLoadingComplete: (complete: boolean) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function LoadingProvider({ children }: { children: ReactNode }) {
   // const [isLoading, setIsLoading] = useState(true);
   // const [isLoadingComplete, setIsLoadingComplete] = useState(false);
   const [isLoading, setIsLoading] = useState(false);
   const [isLoadingComplete, setIsLoadingComplete] = useState(true);

   return (
      <LoadingContext.Provider
         value={{
            isLoading,
            setIsLoading,
            isLoadingComplete,
            setIsLoadingComplete,
         }}
      >
         {children}
      </LoadingContext.Provider>
   );
}

export function useLoading() {
   const context = useContext(LoadingContext);
   if (context === undefined) {
      throw new Error("useLoading must be used within a LoadingProvider");
   }
   return context;
}
