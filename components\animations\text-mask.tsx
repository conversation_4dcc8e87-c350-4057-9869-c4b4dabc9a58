"use client";

import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { SplitText } from "gsap/dist/SplitText";
import { useRef } from "react";

gsap.registerPlugin(ScrollTrigger, SplitText);

type Props = {
   children: React.ReactNode;
   className?: string;
   indent?: string;
   stagger?: number;
};

export default function TextMask({
   children,
   className,
   indent,
   stagger = 0.08,
}: Props) {
   const element = useRef<HTMLDivElement>(null);

   useGSAP(() => {
      const split = SplitText.create(element.current, {
         type: "lines",
         mask: "lines",
         autoSplit: true,
      });

      gsap.from(split.lines, {
         y: 100,
         autoAlpha: 0,
         // ease: "expo.out",
         // stagger: 0.08,
         stagger: stagger,
         scrollTrigger: {
            trigger: element.current,
            // start: "top 80%",
            start: "top 70%",
            end: "bottom 20%",
            // markers: true,
         },
      });
   }, []);

   return (
      <div ref={element} className={className}>
         {indent && (
            <div
               className={`inline-block h-10 w-[50px] sm:w-[150px] md:w-[${indent}]`}
               aria-hidden="true"
            />
         )}
         {children}
      </div>
   );
}
