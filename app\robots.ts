import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
   const baseUrl = "https://www.beckizedek.com";

   return {
      rules: [
         {
            userAgent: "*",
            allow: "/",
            disallow: [
               "/api/",
               "/admin/",
               "/_next/",
               "/private/",
               "*.json",
               "/search",
            ],
         },
         {
            userAgent: "GPTBot",
            disallow: "/",
         },
         {
            userAgent: "ChatGPT-User",
            disallow: "/",
         },
         {
            userAgent: "CCBot",
            disallow: "/",
         },
         {
            userAgent: "anthropic-ai",
            disallow: "/",
         },
         {
            userAgent: "Claude-Web",
            disallow: "/",
         },
      ],
      sitemap: `${baseUrl}/sitemap.xml`,
      host: baseUrl,
   };
}
