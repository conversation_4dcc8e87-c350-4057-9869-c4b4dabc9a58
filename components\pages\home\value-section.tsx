"use client";

import InfiniteScroll from "@/components/animations/infinite-scroll/infinite-scroll";
import TextMask from "@/components/animations/text-mask";
import SectionIntro from "@/components/ui/section-intro";

const roles = [
   "Creative Director",
   "Content Strategist",
   "Brand Storyteller",
   "Marketing Campaign Lead",
   "Campaign Video Director",
   "Event Storytelling Specialist",
];

const rolesList = roles.flatMap((role, index) => [
   <li key={`role-${index}`}>{role} / </li>,
]);

export default function ValueSection() {
   return (
      <section className="py-40 pt-35" id="what-i-do">
         <SectionIntro
            number="02"
            text="What can I do for You?"
            variant="light"
            className="mb-18 px-10"
         />

         <div className="overflow-hidden">
            <InfiniteScroll
               speed="slow"
               direction="left"
               className="font-editorial-old mx-auto mb-20 w-full max-w-full text-5xl font-thin tracking-wider text-blue-950 uppercase sm:text-7xl md:text-8xl lg:text-9xl"
               showMask={false}
            >
               {rolesList}
            </InfiniteScroll>
         </div>

         <div className="space-y-40 px-7 pt-[5rem] sm:px-10 md:space-y-60">
            {/* <motion.div className="font-editorial-old relative text-[clamp(40px,3.2rem,4rem)] font-thin tracking-wide"> */}
            <div className="font-editorial-old relative text-[1.8rem] font-thin tracking-wide md:text-[2.8rem] lg:text-[3.2rem]">
               <TextMask
                  className="text-primary leading-12 sm:leading-14 md:leading-17"
                  indent="300px"
               >
                  How did one simple ad creative help a struggling food brand
                  generate over ₦11 million in sales?
               </TextMask>
            </div>

            <div className="font-gilroy text-primary mx-auto max-w-5xl space-y-4 text-justify text-[1.5rem] leading-10 font-medium tracking-wide sm:text-center md:text-[1.8rem] md:leading-12 lg:text-[2rem]">
               <TextMask>
                  <p>
                     It wasn’t a funnel or influencer chaos. It was a clear,
                     confident piece of content that did exactly what it was
                     meant to do: connect. That ad sparked 600+ conversations
                     and converted 300+ real sales for a brand owner who was
                     ready to quit.
                  </p>
               </TextMask>
            </div>

            <div className="font-editorial-old relative text-[2.2rem] font-thin tracking-wide md:text-[3rem] lg:text-[3.5rem]">
               <TextMask
                  className="text-primary leading-14 md:leading-17"
                  indent="300px"
               >
                  I work with business owners and creative-led brands who want
                  to grow beyond aesthetics, with a strategy that delivers real,
                  measurable results.
               </TextMask>
            </div>
         </div>
      </section>
   );
}
