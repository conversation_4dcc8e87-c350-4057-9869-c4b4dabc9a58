{"name": "becki<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.2", "@next/third-parties": "^15.5.6", "@react-three/drei": "^10.7.6", "@react-three/fiber": "^9.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lenis": "^1.3.11", "lucide-react": "^0.539.0", "motion": "^12.23.12", "next": "^15.5.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "three": "^0.180.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}