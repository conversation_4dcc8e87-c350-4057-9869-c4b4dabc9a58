"use client";

import { motion } from "motion/react";
import Link from "next/link";
import NavigationMenu from "./navigation-menu";
import { useLoading } from "./providers/loading-provider";

export default function Navigation() {
   const { isLoadingComplete } = useLoading();

   return (
      <>
         {/* Navigation Menu */}
         <NavigationMenu isVisible={isLoadingComplete} />

         <motion.div
            className="sticky top-0 z-100 flex h-[80px] flex-row items-center justify-between px-5 py-5 pr-8 text-base font-medium tracking-wider"
            initial={{ y: -100 }}
            animate={{
               y: !isLoadingComplete ? -100 : 0,
            }}
            transition={{
               duration: isLoadingComplete ? 0.4 : 0.8,
               delay: isLoadingComplete ? 0 : 0.2,
               ease: [0.25, 0.46, 0.45, 0.94],
            }}
         >
            <motion.div
               initial={{ y: -50, opacity: 0 }}
               animate={
                  isLoadingComplete
                     ? { y: 0, opacity: 1 }
                     : { y: -50, opacity: 0 }
               }
               transition={{
                  duration: 0.6,
                  delay: isLoadingComplete ? 0.4 : 0,
                  ease: [0.25, 0.46, 0.45, 0.94],
               }}
            >
               <Link
                  href="/"
                  className="text-primary font-serif text-2xl font-thin italic"
               >
                  Beckizedek
               </Link>
            </motion.div>
         </motion.div>
      </>
   );
}
