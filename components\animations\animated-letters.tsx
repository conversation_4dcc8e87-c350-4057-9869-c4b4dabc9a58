"use client";

import { motion } from "framer-motion";

const letterAni = {
   initial: { y: 400, rotate: 0 },
   animate: (custom: number) => ({
      y: 0,
      rotate: 0,
      transition: {
         // ease: [0.83, 0, 0.17, 1] as const,
         // ease: "easeInOut" as const,
         // duration: 1,
         duration: 5,
         delay: custom,
         type: "spring" as const,
         stiffness: 200,
         damping: 21,
      },
   }),
};

type Props = {
   title: string;
   delay?: number;
   direction?: "forwards" | "reversed";
};

export const AnimatedLetters = ({
   title,
   delay = 0,
   direction = "forwards",
}: Props) => (
   <motion.span
      className="font-anton text-primary relative col-span-12 mt-auto inline-block overflow-hidden py-4 text-center text-[20vw] leading-[0.9] font-bold tracking-wider whitespace-nowrap uppercase sm:text-[clamp(2rem,21.5vw,39rem)] sm:tracking-wide"
      initial="initial"
      animate="animate"
   >
      {[...title].map((letter, i, arr) => {
         const index = direction === "reversed" ? arr.length - 1 - i : i;
         return (
            <motion.span
               className="relative inline-block whitespace-nowrap"
               variants={letterAni}
               // custom={delay + index * 0.075}
               custom={delay + index * 0.025}
               key={letter + i}
            >
               {letter}
            </motion.span>
         );
      })}
   </motion.span>
);
