import { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
   const baseUrl = "https://www.beckizedek.com";
   const currentDate = new Date();

   return [
      {
         url: baseUrl,
         lastModified: currentDate,
         changeFrequency: "monthly",
         priority: 1,
      },
      {
         url: `${baseUrl}/#about`,
         lastModified: currentDate,
         changeFrequency: "monthly",
         priority: 0.9,
      },
      {
         url: `${baseUrl}/#what-i-do`,
         lastModified: currentDate,
         changeFrequency: "monthly",
         priority: 0.8,
      },
      {
         url: `${baseUrl}/#services`,
         lastModified: currentDate,
         changeFrequency: "monthly",
         priority: 0.9,
      },
      {
         url: `${baseUrl}/#brands`,
         lastModified: currentDate,
         changeFrequency: "monthly",
         priority: 0.7,
      },
      {
         url: `${baseUrl}/#contact`,
         lastModified: currentDate,
         changeFrequency: "monthly",
         priority: 0.8,
      },
   ];
}
