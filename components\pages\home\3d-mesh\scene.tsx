import { Canvas } from "@react-three/fiber";
import { MotionValue } from "motion/react";
import React from "react";
import Model from "./model";

type Props = {
   scrollProgress: MotionValue<number>;
};

export default function Scene({ scrollProgress }: Props) {
   return (
      <Canvas>
         <Model
            scrollProgress={scrollProgress}
            imageSrc="/images/becky/becky.png"
         />
      </Canvas>
   );
}
