"use client";

import { useInView } from "framer-motion";
import { motion } from "motion/react";
import { useRef } from "react";

interface SectionIntroProps {
   number: string;
   text: string;
   variant?: "light" | "dark";
   className?: string;
}

export default function SectionIntro({
   number,
   text,
   variant = "dark",
   className = "",
}: SectionIntroProps) {
   const ref = useRef(null);
   const isInView = useInView(ref, { once: true, margin: "-100px" });

   const textColor = variant === "light" ? "text-gray-800" : "text-gray-300";
   const numberColor = variant === "light" ? "text-gray-900" : "text-gray-400";

   return (
      <div ref={ref} className={`text-center ${className}`}>
         <motion.div
            className={`font-editorial-old mb-6 text-4xl font-thin ${numberColor}`}
            initial={{
               opacity: 0,
               y: 30,
               filter: "blur(20px)",
            }}
            animate={
               isInView
                  ? {
                       opacity: 1,
                       y: 0,
                       filter: "blur(0px)",
                    }
                  : {}
            }
            transition={{
               duration: 0.4,
               ease: [0.25, 0.46, 0.45, 0.94],
               delay: 0.1,
            }}
         >
            ({number})
         </motion.div>

         <motion.div
            className={`font-gilroy mb-12 text-base font-bold uppercase ${textColor}`}
            initial={{
               opacity: 0,
               y: 30,
               filter: "blur(20px)",
            }}
            animate={
               isInView
                  ? {
                       opacity: 1,
                       y: 0,
                       filter: "blur(0px)",
                    }
                  : {}
            }
            transition={{
               duration: 0.4,
               ease: [0.25, 0.46, 0.45, 0.94],
               delay: 0.2,
            }}
         >
            <h3>{text}</h3>
         </motion.div>
      </div>
   );
}
