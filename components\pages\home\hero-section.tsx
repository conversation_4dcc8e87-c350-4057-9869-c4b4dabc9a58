"use client";

import { AnimatedLetters } from "@/components/animations/animated-letters";
import CharacterReveal from "@/components/animations/character-reveal";
import { useLoading } from "@/components/providers/loading-provider";
import { motion } from "motion/react";
import Image from "next/image";

export default function HeroSection() {
   const { isLoadingComplete } = useLoading();

   return (
      <>
         <div className="fixed top-0 right-0 bottom-0 left-0 flex h-[100dvh] flex-col items-center justify-between pt-[80px]">
            <div className="grid h-full w-full grid-cols-12 grid-rows-1 items-end">
               <motion.div
                  className="align-self-end 3xl:max-w-lg relative col-span-4 flex h-full flex-col items-start justify-end gap-4 pr-1 pb-[10vh] pl-5 sm:max-w-xs lg:max-w-md"
                  initial="initial"
                  animate={isLoadingComplete ? "animate" : "initial"}
                  variants={{
                     initial: {},
                     animate: {
                        transition: {
                           staggerChildren: 0.1,
                        },
                     },
                  }}
               >
                  <motion.p
                     // className="font-editorial-old text-primary text-4xl font-thin tracking-wider"
                     className="font-gilroy text-primary 3xl:text-5xl 3xl:leading-15 hidden text-2xl tracking-wider lg:block xl:text-3xl"
                     variants={{
                        initial: {
                           y: 30,
                           opacity: 0,
                           filter: "blur(20px)",
                        },
                        animate: {
                           y: 0,
                           opacity: 1,
                           filter: "blur(0px)",
                           transition: {
                              duration: 0.8,
                              ease: [0.25, 0.46, 0.45, 0.94],
                              delay: 1.1,
                           },
                        },
                     }}
                  >
                     I build brands that{" "}
                     <span className="font-serif text-3xl font-thin tracking-widest italic xl:text-4xl">
                        connect
                     </span>
                     ,{" "}
                     <span className="font-serif text-3xl font-thin tracking-widest italic xl:text-4xl">
                        convert
                     </span>
                     , and{" "}
                     <span className="font-serif text-3xl font-thin tracking-widest italic xl:text-4xl">
                        command
                     </span>{" "}
                     attention.
                  </motion.p>
               </motion.div>

               {/* <CharacterReveal animate={isLoadingComplete}>
                  Beckizedek
               </CharacterReveal> */}
               <AnimatedLetters title="Beckizedek" />
               {/* <motion.div
                  className="absolute top-1/2 left-1/2 -translate-1/2 md:top-10 md:right-8 md:left-auto md:translate-0"
                  initial="initial"
                  animate={isLoadingComplete ? "animate" : "initial"}
                  variants={{
                     initial: {
                        clipPath:
                           "polygon(0 100%, 100% 100%, 100% 100%, 0% 100%)",
                        filter: "brightness(3) contrast(2.5)",
                        scale: 1.2,
                     },
                     animate: {
                        clipPath: "polygon(0 100%, 100% 100%, 100% 0%, 0% 0%)",
                        filter: "brightness(1) contrast(1)",
                        scale: 1,
                        transition: {
                           duration: 0.8,
                           // delay: 0.3,
                           delay: 1,
                           ease: [0.25, 0.46, 0.45, 0.94],
                        },
                     },
                  }}
               >
                  <Image
                     src="/images/becky/becky-4.png"
                     alt="Beckizedek - Creative Director and Brand Storyteller"
                     width={500}
                     height={500}
                     className="3xl:max-w-[none] 3xl:w-[70rem] 3xl:h-[70rem] h-[600px] max-h-[70vh] max-w-[90vw] object-cover sm:h-[500px] sm:max-w-92"
                     priority
                  />
               </motion.div> */}
            </div>
         </div>
      </>
   );
}
