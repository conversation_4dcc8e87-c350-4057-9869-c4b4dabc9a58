"use client";

import { ReactNode } from "react";
import GlobalLoading from "./animations/global-loading";
import Footer from "./footer";
import Navigation from "./navigation";
import { useLoading } from "./providers/loading-provider";

interface ClientLayoutProps {
   children: ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
   const { isLoading, setIsLoading, setIsLoadingComplete } = useLoading();

   const handleLoadingComplete = () => {
      setIsLoading(false);
      setIsLoadingComplete(true);
   };

   return (
      <>
         {isLoading && <GlobalLoading onComplete={handleLoadingComplete} />}
         <Navigation />
         {children}
         <Footer />
      </>
   );
}
