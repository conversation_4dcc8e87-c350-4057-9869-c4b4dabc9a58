import InfiniteLogoScroll from "@/components/animations/infinite-logo-scroll";
import TextMask from "@/components/animations/text-mask";
import SectionIntro from "@/components/ui/section-intro";

const logos = [
   {
      src: "/images/logos/bal.jpg",
      alt: "BAL - Trusted brand partner of Beckizedek",
   },
   {
      src: "/images/logos/gino.png",
      alt: "Gino - Client brand logo",
   },
   {
      src: "/images/logos/fael.png",
      alt: "FAEL - Brand partner logo",
      className: "invert",
   },
   {
      src: "/images/logos/oiza.png",
      alt: "Oiza - Client brand logo",
   },
   {
      src: "/images/logos/grange-school.jpg",
      alt: "Grange School - Educational institution client",
   },
   {
      src: "/images/logos/the-game-house.png",
      alt: "The Game House - Entertainment brand client",
   },
   {
      src: "/images/logos/tuntun-yums.jpg",
      alt: "Tun Tun Yums - Food brand client",
      className: "invert",
   },
];

export default function BrandsSection() {
   return (
      <section className="py-40 pb-20" id="brands">
         <SectionIntro
            number="04"
            text="Trusted By Brands That Lead"
            variant="light"
            className="px-10 md:mb-18"
         />

         <TextMask className="font-editorial-old text-primary px-5 pt-10 pb-22 text-center text-3xl leading-11 font-thin tracking-wide md:text-4xl md:leading-15 lg:text-5xl">
            I&apos;ve had the privilege of partnering with big and emerging
            brands to bring their stories to life.
         </TextMask>

         {/* Infinite scrolling logos with scroll interaction */}
         <InfiniteLogoScroll logos={logos} direction="left" />

         {/* Second row moving in opposite direction */}
         <InfiniteLogoScroll
            logos={logos.slice().reverse()}
            direction="right"
         />
      </section>
   );
}
