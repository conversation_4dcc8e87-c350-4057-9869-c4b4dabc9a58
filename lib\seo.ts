import { Metadata } from "next";

// SEO Configuration Types
export interface SEOConfig {
   title: string;
   description: string;
   keywords?: string[];
   author?: string;
   url?: string;
   image?: string;
   type?: "website" | "article" | "profile";
   siteName?: string;
   locale?: string;
   alternateLocales?: string[];
}

// Default SEO Configuration
export const defaultSEO: SEOConfig = {
   title: "Beckizedek - Creative Director & Brand Storyteller",
   description:
      "Media, Marketing & Communications Creative specializing in brand storytelling, content creation, and strategic campaigns that deliver measurable results.",
   keywords: [
      "creative director",
      "brand storyteller",
      "content strategist",
      "marketing campaigns",
      "video director",
      "brand communications",
      "content creation",
      "digital marketing",
      "creative strategy",
      "brand development",
   ],
   author: "Beckizedek",
   url: "https://www.beckizedek.com",
   image: "/images/becky/og-image.png",
   type: "website",
   siteName: "Beckizedek",
   locale: "en_US",
};

// Generate comprehensive metadata for Next.js
export function generateMetadata(config: Partial<SEOConfig> = {}): Metadata {
   const seo = { ...defaultSEO, ...config };

   return {
      title: seo.title,
      description: seo.description,
      keywords: seo.keywords?.join(", "),
      authors: seo.author ? [{ name: seo.author }] : undefined,
      creator: seo.author,
      publisher: seo.author,
      metadataBase: new URL("https://www.beckizedek.com"),

      // Open Graph
      openGraph: {
         title: seo.title,
         description: seo.description,
         url: seo.url,
         siteName: seo.siteName,
         images: seo.image
            ? [
                 {
                    url: seo.image,
                    width: 1200,
                    height: 630,
                    alt: seo.title,
                 },
              ]
            : undefined,
         locale: seo.locale,
         type: seo.type,
      },

      // Twitter Card
      twitter: {
         card: "summary_large_image",
         title: seo.title,
         description: seo.description,
         images: seo.image ? [seo.image] : undefined,
         creator: "@Ogunleyepraise1",
         site: "@Ogunleyepraise1",
      },

      // Additional meta tags
      robots: {
         index: true,
         follow: true,
         googleBot: {
            index: true,
            follow: true,
            "max-video-preview": -1,
            "max-image-preview": "large",
            "max-snippet": -1,
         },
      },

      // Verification tags (add your verification codes here)
      // verification: {
      //    google: "", // Add Google Search Console verification code
      //    // yandex: "",
      //    // yahoo: "",
      //    // other: "",
      // },

      // Canonical URL
      alternates: {
         canonical: seo.url,
      },

      // Additional metadata
      category: "Creative Services",
      classification: "Business",

      // App-specific metadata
      applicationName: seo.siteName,
      referrer: "origin-when-cross-origin",

      // Icons
      icons: {
         icon: "/favicon.ico",
         shortcut: "/favicon.ico",
      },
   };
}

// Structured Data Schemas
export interface OrganizationSchema {
   "@context": "https://schema.org";
   "@type": "Organization" | "Person";
   name: string;
   url: string;
   logo?: string;
   image?: string;
   description: string;
   sameAs?: string[];
   contactPoint?: {
      "@type": "ContactPoint";
      email: string;
      contactType: string;
   };
   address?: {
      "@type": "PostalAddress";
      addressCountry: string;
      addressRegion?: string;
   };
}

export interface ServiceSchema {
   "@context": "https://schema.org";
   "@type": "Service";
   name: string;
   description: string;
   provider: {
      "@type": "Organization" | "Person";
      name: string;
   };
   serviceType: string;
   areaServed?: string;
}

export interface WebSiteSchema {
   "@context": "https://schema.org";
   "@type": "WebSite";
   name: string;
   url: string;
   description: string;
   potentialAction?: {
      "@type": "SearchAction";
      target: string;
      "query-input": string;
   };
}

// Generate Organization/Person Schema
export function generateOrganizationSchema(): OrganizationSchema {
   return {
      "@context": "https://schema.org",
      "@type": "Person",
      name: "Beckizedek",
      url: "https://www.beckizedek.com",
      image: "https://www.beckizedek.com/images/becky/becky.png",
      description:
         "Creative Director, Brand Storyteller, and Marketing Communications specialist helping brands grow with strategic content and campaigns.",
      sameAs: [
         "https://www.instagram.com/beckizedech",
         "https://www.linkedin.com/in/becky-kolawole",
         "https://www.tiktok.com/@beckizedech",
      ],
      contactPoint: {
         "@type": "ContactPoint",
         email: "<EMAIL>",
         contactType: "Business Inquiries",
      },
      address: {
         "@type": "PostalAddress",
         addressCountry: "NG",
      },
   };
}

// Generate Services Schema
export function generateServicesSchema(): ServiceSchema[] {
   const services = [
      {
         name: "Impact Content Creation",
         description:
            "High-quality content filmed and edited for maximum brand impact, including reels, ads, and storytelling campaigns.",
         serviceType: "Content Creation",
      },
      {
         name: "Revenue Ad System",
         description:
            "Complete advertising solution from strategy to execution, including creative development and campaign optimization.",
         serviceType: "Digital Marketing",
      },
      {
         name: "Brand Strategy & Communications",
         description:
            "Strategic brand development, messaging, and communications planning for business growth.",
         serviceType: "Brand Strategy",
      },
   ];

   return services.map((service) => ({
      "@context": "https://schema.org",
      "@type": "Service",
      name: service.name,
      description: service.description,
      provider: {
         "@type": "Person",
         name: "Beckizedek",
      },
      serviceType: service.serviceType,
      areaServed: "Global",
   }));
}

// Generate Website Schema
export function generateWebSiteSchema(): WebSiteSchema {
   return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      name: "Beckizedek",
      url: "https://www.beckizedek.com",
      description:
         "Creative Director & Brand Storyteller specializing in strategic content creation and marketing campaigns.",
      // potentialAction: {
      //    "@type": "SearchAction",
      //    target: "https://www.beckizedek.com/search?q={search_term_string}",
      //    "query-input": "required name=search_term_string",
      // },
   };
}

// Generate all structured data
export function generateStructuredData() {
   return {
      organization: generateOrganizationSchema(),
      services: generateServicesSchema(),
      website: generateWebSiteSchema(),
   };
}

// SEO utility functions
export function generatePageTitle(pageTitle?: string): string {
   if (!pageTitle) return defaultSEO.title;
   return `${pageTitle} | ${defaultSEO.siteName}`;
}

export function generatePageDescription(pageDescription?: string): string {
   return pageDescription || defaultSEO.description;
}

export function generateCanonicalUrl(path: string = ""): string {
   const baseUrl = defaultSEO.url || "https://www.beckizedek.com";
   return `${baseUrl}${path}`;
}
