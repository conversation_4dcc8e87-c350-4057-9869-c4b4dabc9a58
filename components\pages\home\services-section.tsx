"use client";

import TextMask from "@/components/animations/text-mask";
import SectionIntro from "@/components/ui/section-intro";
import { motion } from "framer-motion";

const services = [
   {
      title: "Impact Content Creation — Done for You",
      description:
         "Tiered, high-quality content filmed and edited for maximum brand impact. Whether reels, high-conversion ads, or storytelling campaigns — we handle everything from direction to polish.",
   },
   {
      title: "The Revenue Ad System",
      description:
         "Not just ads — results. We start with a deep-dive strategy session, design offers tailored to your audience, bring our media team to your space to film and execute the creative, then run and optimize your ad campaigns for measurable ROI.",
   },
   {
      title: "Videography & Event Coverage",
      description:
         "Full-scale coverage for brand events, corporate functions, and special occasions. From intimate launches to large-scale productions, we capture every moment in cinematic quality — and offer vendor-specific packages to help partners showcase their work.",
   },
   {
      title: "Offer Creation 101 (Creative eBook)",
      description:
         "A digital guide packed with creative, high-converting offer ideas for different industries. Learn what sells, why it sells, and how to package your services or products in ways that make your audience say “yes” instantly.",
   },
   {
      title: "Social Media Management",
      description:
         "We handle the day-to-day of your online presence — creating, scheduling, engaging, and analyzing — so you can focus on running your business while your brand visibility, consistency, and engagement grow.",
   },
   {
      title: "YouTube Monetization Strategy",
      description:
         "Turn your channel into a revenue stream through smart content planning, SEO, and audience-tailored growth strategies.",
   },
   {
      title: "Video Storytelling Training",
      description:
         "From content planning to SEO optimization, we help you turn your YouTube channel into a revenue-generating machine, with strategies tailored to your niche and audience behavior.",
   },
   {
      title: "Event & Program Publicity Management",
      description:
         "From pre-launch buzz to post-event coverage, we plan and execute promotional campaigns that fill seats, attract attention, and keep people talking about your event long after it’s over.",
   },
   {
      title: "Gospel Media Channel (Special Mention)",
      description:
         "An independent media arm dedicated to creating and sharing gospel-centered content — a passion project that fuels creativity, storytelling, and impact in the faith space.",
   },
];

export default function ServicesSection() {
   return (
      <section className="bg-primary pt-40 pb-15" id="services">
         <div className="px-5">
            <SectionIntro
               number="03"
               text="A Full-Spectrum Creative Partnership"
               variant="dark"
               className="mb-18"
            />
            <TextMask
               className="font-editorial-old pt-10 pb-30 text-center text-3xl leading-12 font-thin tracking-wide text-gray-300 md:text-4xl md:leading-15 lg:text-5xl"
               indent="200px"
            >
               From high-level strategy to hands-on production, my services are
               designed to provide end-to-end solutions for your brand&apos;s
               media, marketing, and communication needs.
            </TextMask>
            <div className="font-editorial-old font-thin">
               {services.map((service, index) => (
                  <motion.div
                     key={index}
                     className="pb-30"
                     initial="hidden"
                     whileInView="visible"
                     viewport={{ once: true, amount: 0.5 }}
                     variants={{
                        hidden: {},
                        visible: {
                           transition: {
                              staggerChildren: 0.4,
                           },
                        },
                     }}
                  >
                     <div className="overflow-hidden">
                        <motion.div
                           className="font-editorial-old text-2xl font-extralight text-gray-400"
                           variants={{
                              hidden: { opacity: 0, y: 20 },
                              visible: {
                                 opacity: 1,
                                 y: 0,
                                 transition: {
                                    duration: 0.8,
                                    ease: "easeOut",
                                 },
                              },
                           }}
                        >
                           0{index + 1}
                        </motion.div>
                     </div>
                     <motion.div
                        className="h-[1px] w-full origin-left bg-gray-400"
                        variants={{
                           hidden: { scaleX: 0 },
                           visible: {
                              scaleX: 1,
                              transition: {
                                 // duration: 1.2,
                                 duration: 0.8,
                                 ease: "easeOut",
                              },
                           },
                        }}
                     />
                     <div className="grid items-start gap-10 pt-5 text-gray-300 md:grid-cols-2">
                        <div className="overflow-hidden">
                           <motion.h2
                              className="text-[1.45rem] font-thin tracking-wider"
                              variants={{
                                 hidden: { opacity: 0, y: 20 },
                                 visible: {
                                    opacity: 1,
                                    y: 0,
                                    transition: {
                                       // duration: 0.8,
                                       duration: 0.6,
                                       ease: "easeOut",
                                    },
                                 },
                              }}
                           >
                              {service.title}
                           </motion.h2>
                        </div>
                        <motion.p
                           // className="text-xl font-thin tracking-wider text-gray-300/80"
                           className="font-gilroy text-lg font-normal tracking-wider text-gray-300/80"
                           variants={{
                              hidden: { opacity: 0, y: 10 },
                              visible: {
                                 opacity: 1,
                                 y: 0,
                                 transition: {
                                    duration: 0.8,
                                    ease: "easeOut",
                                 },
                              },
                           }}
                        >
                           {service.description}
                        </motion.p>
                     </div>
                  </motion.div>
               ))}
            </div>
         </div>
      </section>
   );
}
