import { motion } from "motion/react";

export default function CtaSection() {
   return (
      <section className="bg-primary">
         <div className="bg-background flex justify-center rounded-b-[2rem] p-2 sm:rounded-b-[3rem] sm:p-8 sm:pb-10">
            <motion.div
               initial={{
                  clipPath: "circle(5% at 50% 50%)",
               }}
               whileInView={{
                  clipPath: "circle(100% at 50% 50%)",
               }}
               transition={{ duration: 0.6 }}
               viewport={{ once: true, margin: "-100px" }}
               className="bg-primary overflow-hidden rounded-4xl p-4 py-8 text-gray-300 sm:p-8 md:p-16"
            >
               <motion.p
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  viewport={{ once: true, margin: "-100px" }}
                  className="font-editorial-old text-center text-2xl leading-10 font-thin tracking-wide md:text-4xl md:leading-14 lg:text-5xl lg:leading-16"
               >
                  Whether it&apos;s launching a campaign, covering an event,
                  rebranding a digital product, writing ad scripts, building a
                  communications strategy, or leading creative direction for a
                  new product; I&apos;m the person brands call when they&apos;re
                  ready to be understood, not just seen. If you&apos;re tired of
                  just posting content that don&apos;t convert and you&apos;re
                  ready to build something more intentional, then you&apos;re in
                  luck!
               </motion.p>
            </motion.div>
         </div>
      </section>
   );
}
