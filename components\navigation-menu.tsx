"use client";

import { handleSmoothScroll } from "@/lib/smoothScroll";
import { cn } from "@/lib/utils";
import { use<PERSON>enis } from "lenis/react";
import { ArrowRight } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import TextRoll from "./animations/text-roll";

// Navigation items from the main navigation component
const navItems = [
   {
      label: "About",
      href: "/#about",
   },
   {
      label: "What I do",
      href: "/#what-i-do",
   },
   {
      label: "Services",
      href: "/#services",
   },
   {
      label: "Trusted Brands",
      href: "/#brands",
   },
   {
      label: "Contact Me",
      href: "/#contact",
   },
];

// Contact links from footer component
const contactLinks = [
   {
      name: "Email",
      href: "mailto:<EMAIL>",
      text: "<EMAIL>",
   },
   {
      name: "Instagram",
      href: "https://www.instagram.com/beckizedech?igsh=MXBhY3A4MXp1MmV3NA==",
      text: "@beckizedech",
   },
   {
      name: "LinkedIn",
      href: "https://www.linkedin.com/in/becky-kolawole?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app",
      text: "in/becky-kolawole",
   },
   {
      name: "TikTok",
      href: " https://www.tiktok.com/@beckizedech?_t=ZM-8yiYsm7XWie&_r=1",
      text: "@beckizedech",
   },
];

interface NavigationMenuProps {
   isVisible?: boolean;
}

export default function NavigationMenu({
   isVisible = true,
}: NavigationMenuProps) {
   const [isOpen, setIsOpen] = useState(false);
   const [showContent, setShowContent] = useState(false);
   const menuRef = useRef<HTMLDivElement>(null);
   const toggleButtonRef = useRef<HTMLButtonElement>(null);
   const firstFocusableRef = useRef<HTMLAnchorElement>(null);
   const lenis = useLenis();

   // Prevent body scroll when menu is open
   useEffect(() => {
      if (isOpen) {
         document.body.style.overflow = "hidden";
         // Delay content animation to start after panel slide-in
         const timer = setTimeout(() => setShowContent(true), 300);
         return () => clearTimeout(timer);
      } else {
         document.body.style.overflow = "unset";
         setShowContent(false);
      }

      return () => {
         document.body.style.overflow = "unset";
      };
   }, [isOpen]);

   // Handle keyboard navigation and focus management
   useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
         if (!isOpen) return;

         if (e.key === "Escape") {
            setIsOpen(false);
            toggleButtonRef.current?.focus();
         }

         if (e.key === "Tab") {
            const focusableElements = menuRef.current?.querySelectorAll(
               'a[href], button, [tabindex]:not([tabindex="-1"])',
            );

            if (focusableElements && focusableElements.length > 0) {
               const firstElement = focusableElements[0] as HTMLElement;
               const lastElement = focusableElements[
                  focusableElements.length - 1
               ] as HTMLElement;

               if (e.shiftKey && document.activeElement === firstElement) {
                  e.preventDefault();
                  lastElement.focus();
               } else if (
                  !e.shiftKey &&
                  document.activeElement === lastElement
               ) {
                  e.preventDefault();
                  firstElement.focus();
               }
            }
         }
      };

      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
   }, [isOpen]);

   // Close menu when clicking outside
   useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
         if (
            menuRef.current &&
            !menuRef.current.contains(event.target as Node) &&
            toggleButtonRef.current &&
            !toggleButtonRef.current.contains(event.target as Node)
         ) {
            setIsOpen(false);
         }
      };

      if (isOpen) {
         document.addEventListener("mousedown", handleClickOutside);
      }

      return () => {
         document.removeEventListener("mousedown", handleClickOutside);
      };
   }, [isOpen]);

   const toggleMenu = () => {
      setIsOpen(!isOpen);

      // Focus management: focus first menu item when opening
      if (!isOpen) {
         setTimeout(() => {
            firstFocusableRef.current?.focus();
         }, 400); // Wait for slide-in animation to complete
      }
   };

   const closeMenu = () => {
      setIsOpen(false);
      toggleButtonRef.current?.focus();
   };

   if (!isVisible) return null;

   return (
      <>
         {/* Menu Toggle Button */}
         <motion.button
            ref={toggleButtonRef}
            onClick={toggleMenu}
            className={cn(
               "active:ring-none focus:ring-none bg-primary/90 hover:bg-primary border-background/10 fixed top-4 right-5 z-[200] flex h-20 w-20 cursor-pointer items-center justify-center rounded-full border backdrop-blur-sm transition-colors focus:ring-0 focus:outline-none",
            )}
            aria-label={isOpen ? "Close menu" : "Open menu"}
            aria-expanded={isOpen}
            aria-controls="mobile-menu"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{
               duration: 0.6,
               delay: 1.1,
               type: "spring",
               stiffness: 400,
               damping: 25,
            }}
         >
            <div className="relative h-6 w-6">
               {/* First line */}
               <motion.div
                  className="absolute top-1/2 left-1/2 h-0.5 w-6 bg-gray-200"
                  style={{
                     originX: 0.5,
                     originY: 0.5,
                     rotate: 0,
                     y: -4,
                     x: "-50%",
                  }}
                  animate={{
                     rotate: isOpen ? 45 : 0,
                     y: isOpen ? 0 : -4,
                  }}
                  transition={{
                     duration: 1,
                     ease: [1, 0, 0, 1],
                  }}
               />
               {/* Second line */}
               <motion.div
                  className="absolute top-1/2 left-1/2 h-0.5 w-6 bg-gray-200"
                  style={{
                     originX: 0.5,
                     originY: 0.5,
                     rotate: 0,
                     y: 4,
                     x: "-50%",
                  }}
                  animate={{
                     rotate: isOpen ? -45 : 0,
                     y: isOpen ? 0 : 4,
                  }}
                  transition={{
                     duration: 1,
                     ease: [1, 0, 0, 1],
                  }}
               />
            </div>
         </motion.button>

         {/* Menu Overlay and Panel */}
         <AnimatePresence>
            {isOpen && (
               <>
                  {/* Menu Panel */}
                  <motion.div
                     ref={menuRef}
                     id="mobile-menu"
                     role="dialog"
                     aria-modal="true"
                     aria-labelledby="mobile-menu-title"
                     className={cn(
                        "bg-primary fixed top-0 left-0 z-[160] h-full w-screen text-white shadow-2xl",
                     )}
                     initial={{ x: "-100%" }}
                     animate={{ x: 0 }}
                     exit={{ x: "-100%" }}
                     transition={{ duration: 1, ease: [1, 0, 0, 1] }}
                  >
                     <div className="flex h-full flex-col p-6 pt-20">
                        {/* Hidden title for screen readers */}
                        <h2 id="mobile-menu-title" className="sr-only">
                           Navigation Menu
                        </h2>

                        {/* Beckizedek Logo */}
                        <div className="absolute top-7 left-5">
                           <Link
                              href="/"
                              onClick={closeMenu}
                              className="font-serif text-2xl font-thin text-white italic"
                           >
                              Beckizedek
                           </Link>
                        </div>

                        {/* Navigation Items */}
                        <nav
                           className="mt-5 mb-auto"
                           role="navigation"
                           aria-label="Main navigation"
                        >
                           <motion.ul className="space-y-4">
                              {navItems.map((item, index) => (
                                 <motion.li
                                    key={item.label}
                                    initial={{ x: -50, opacity: 0 }}
                                    animate={
                                       showContent
                                          ? { x: 0, opacity: 1 }
                                          : { x: -50, opacity: 0 }
                                    }
                                    transition={{
                                       duration: 0.4,
                                       delay: index * 0.1,
                                       ease: "easeOut",
                                    }}
                                 >
                                    <Link
                                       ref={
                                          index === 0
                                             ? firstFocusableRef
                                             : undefined
                                       }
                                       href={item.href}
                                       onClick={(e) =>
                                          handleSmoothScroll(
                                             e,
                                             item.href,
                                             lenis,
                                             closeMenu,
                                          )
                                       }
                                       className="font-gilroy inline-block text-4xl font-semibold tracking-wider text-white/90 uppercase transition-colors hover:text-white md:text-5xl lg:text-7xl"
                                    >
                                       <TextRoll center>{item.label}</TextRoll>
                                    </Link>
                                 </motion.li>
                              ))}
                           </motion.ul>
                        </nav>

                        {/* Email Section */}
                        <motion.div
                           className="mb-8"
                           initial={{ y: 30, opacity: 0 }}
                           animate={
                              showContent
                                 ? { y: 0, opacity: 1 }
                                 : { y: 30, opacity: 0 }
                           }
                           transition={{
                              duration: 0.5,
                              delay: navItems.length * 0.1 + 0.2,
                              ease: "easeOut",
                           }}
                        >
                           <Link
                              href={contactLinks[0].href}
                              onClick={closeMenu}
                              className={cn(
                                 "font-editorial-old group relative flex w-fit items-center text-base font-thin tracking-widest text-[#F2F2F2] uppercase",
                              )}
                           >
                              <TextRoll center>{contactLinks[0].text}</TextRoll>
                              {/* <ArrowRight className="z-0 mt-[0em] ml-[0.6em] size-4 -translate-x-1 -translate-y-[2.5px] opacity-0 transition-all duration-300 group-hover:translate-x-0 group-hover:opacity-100" /> */}
                           </Link>
                        </motion.div>

                        {/* Social Media Section */}
                        <motion.div
                           initial={{ y: 30, opacity: 0 }}
                           animate={
                              showContent
                                 ? { y: 0, opacity: 1 }
                                 : { y: 30, opacity: 0 }
                           }
                           transition={{
                              duration: 0.5,
                              delay: navItems.length * 0.1 + 0.2,
                              ease: "easeOut",
                           }}
                        >
                           <div className="space-y-4">
                              {contactLinks.slice(1).map((link) => (
                                 <div key={link.name}>
                                    <Link
                                       href={link.href}
                                       onClick={closeMenu}
                                       className={cn(
                                          "font-editorial-old group relative flex items-center text-sm font-thin tracking-widest text-[#F2F2F2] uppercase",
                                       )}
                                    >
                                       {link.name}
                                       <ArrowRight className="z-0 mt-[0em] ml-[0.6em] size-4 -translate-x-1 -translate-y-[2.5px] opacity-0 transition-all duration-300 group-hover:translate-x-0 group-hover:opacity-100" />
                                    </Link>
                                 </div>
                              ))}
                           </div>
                        </motion.div>
                     </div>
                  </motion.div>
               </>
            )}
         </AnimatePresence>
      </>
   );
}
