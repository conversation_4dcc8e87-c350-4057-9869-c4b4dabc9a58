import localFont from "next/font/local";

export const editorialOld = localFont({
   src: [
      {
         path: "../public/fonts/editorial-old-font/ppeditorialold-ultralight.otf",
         weight: "200",
         style: "normal",
      },
      {
         path: "../public/fonts/editorial-old-font/ppeditorialold-ultralightitalic.otf",
         weight: "200",
         style: "italic",
      },
      {
         path: "../public/fonts/editorial-old-font/ppeditorialold-regular.otf",
         weight: "400",
         style: "normal",
      },
      {
         path: "../public/fonts/editorial-old-font/ppeditorialold-italic.otf",
         weight: "400",
         style: "italic",
      },
      {
         path: "../public/fonts/editorial-old-font/ppeditorialold-ultrabold.otf",
         weight: "800",
         style: "normal",
      },
      {
         path: "../public/fonts/editorial-old-font/ppeditorialold-ultrabolditalic.otf",
         weight: "800",
         style: "italic",
      },
   ],
   variable: "--font-editorial-old",
});

export const gilroy = localFont({
   src: [
      {
         path: "../public/fonts/gilroy/Gilroy-Thin.ttf",
         weight: "100",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-ThinItalic.ttf",
         weight: "100",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-UltraLight.ttf",
         weight: "200",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-UltraLightItalic.ttf",
         weight: "200",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-Light.ttf",
         weight: "300",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-LightItalic.ttf",
         weight: "300",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-Regular.ttf",
         weight: "400",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-RegularItalic.ttf",
         weight: "400",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-Medium.ttf",
         weight: "500",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-MediumItalic.ttf",
         weight: "500",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-SemiBold.ttf",
         weight: "600",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-SemiBoldItalic.ttf",
         weight: "600",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-Bold.ttf",
         weight: "700",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-BoldItalic.ttf",
         weight: "700",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-ExtraBold.ttf",
         weight: "800",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-ExtraBoldItalic.ttf",
         weight: "800",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-Heavy.ttf",
         weight: "900",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-HeavyItalic.ttf",
         weight: "900",
         style: "italic",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-Black.ttf",
         weight: "950",
         style: "normal",
      },
      {
         path: "../public/fonts/gilroy/Gilroy-BlackItalic.ttf",
         weight: "950",
         style: "italic",
      },
   ],
   variable: "--font-gilroy",
});
