import type Lenis from "lenis";

/**
 * Smooth scroll utility function for handling anchor link navigation
 *
 * @param e - The mouse event from the link click
 * @param href - The href attribute of the link
 * @param lenis - The Lenis instance for smooth scrolling
 * @param onComplete - Optional callback function to execute after scrolling (e.g., closing menus)
 */
export const handleSmoothScroll = (
   e: React.MouseEvent<HTMLAnchorElement>,
   href: string,
   lenis: Lenis | undefined,
   onComplete?: () => void,
) => {
   // Only handle anchor links (starting with /#)
   if (href.startsWith("/#")) {
      e.preventDefault();
      const targetId = href.substring(2); // Remove "/#" to get the id
      const targetElement = document.getElementById(targetId);

      if (targetElement && lenis) {
         lenis.scrollTo(targetElement, {
            duration: 1.2,
            easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // Smooth easing without bounce
         });
      }

      // Execute callback if provided (e.g., close menu)
      if (onComplete) {
         onComplete();
      }
   }
};
