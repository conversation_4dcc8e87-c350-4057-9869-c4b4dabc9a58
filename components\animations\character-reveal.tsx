"use client";

import { motion } from "motion/react";

interface CharacterRevealProps {
   children: string;
   animate?: boolean;
}

export default function CharacterReveal({
   children,
   animate = true,
}: CharacterRevealProps) {
   return (
      <motion.h1
         className="font-anton text-primary relative col-span-12 mt-auto text-center text-[20vw] leading-[0.9] font-bold tracking-wider uppercase sm:text-[clamp(2rem,21.5vw,39rem)] sm:tracking-wide"
         initial="initial"
         animate={animate ? "animate" : "initial"}
         variants={{
            initial: {},
            animate: {
               transition: {
                  staggerChildren: 0.055,
                  // staggerChildren: 0.08,
               },
            },
         }}
      >
         {children.split("").map((char, index) => (
            <motion.span
               key={index}
               className="inline-block overflow-hidden"
               variants={{
                  initial: {
                     clipPath: "polygon(0 100%, 100% 100%, 100% 100%, 0% 100%)",
                     y: 20,
                  },
                  animate: {
                     clipPath: "polygon(0 100%, 100% 100%, 100% 0%, 0% 0%)",
                     y: 0,
                     transition: {
                        duration: 0.5,
                        ease: [0.25, 0.46, 0.45, 0.94], // Custom easing for smooth reveal
                     },
                  },
               }}
            >
               {char === " " ? "\u00A0" : char}
            </motion.span>
         ))}
      </motion.h1>
   );
}
