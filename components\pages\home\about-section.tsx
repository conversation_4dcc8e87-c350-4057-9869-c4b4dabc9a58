"use client";

import TextMask from "@/components/animations/text-mask";
import TextRoll from "@/components/animations/text-roll";
import SectionIntro from "@/components/ui/section-intro";
import { handleSmoothScroll } from "@/lib/smoothScroll";
import { use<PERSON>enis } from "lenis/react";
import Link from "next/link";

export default function AboutSection() {
   const lenis = useLenis();

   return (
      <section
         id="about"
         className="bg-primary min-h-screen overflow-hidden py-40"
      >
         <div className="container mx-auto px-7 sm:px-10">
            <div className="mx-auto max-w-6xl text-center text-gray-400">
               <SectionIntro number="01" text="Who am I?" variant="dark" />
               <div className="font-editorial-old space-y-10 text-2xl leading-13 font-thin tracking-wider text-gray-200 md:text-3xl md:leading-14 lg:text-4xl lg:leading-15">
                  <TextMask stagger={0.05}>
                     <p>
                        The Brand is Beckizedek, but many know me as a Media,
                        Marketing & Communications Creative whiz and Brand
                        Storyteller. I work with business owners and
                        creative-led brands who want to grow beyond aesthetics,
                        but with strategy that delivers. I bring structure to
                        their story, power to their content, and clarity to
                        their messaging. My approach blends deep thinking with
                        real creativity: the kind that gets people to stop
                        scrolling, pay attention, and take action.
                     </p>
                  </TextMask>
               </div>

               <Link
                  href="/#contact"
                  onClick={(e) => handleSmoothScroll(e, "/#contact", lenis)}
                  className="group font-gilroy mx-auto mt-15 flex w-fit cursor-pointer rounded-full bg-white px-6 py-2 text-lg font-medium tracking-wide text-gray-800 transition-all duration-400"
               >
                  <span className="mr-2 inline-block transition-all duration-400 group-hover:-translate-x-2">
                     {"["}
                  </span>
                  <TextRoll center>Book free consultation</TextRoll>
                  <span className="ml-2 inline-block transition-all duration-400 group-hover:translate-x-2">
                     {"]"}
                  </span>
               </Link>
            </div>
         </div>
      </section>
   );
}
